@import "tailwindcss";

@font-face {
    font-family: 'Revain';
    src: url('./fonts/RevainRegular-7OjPR.ttf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: '<PERSON><PERSON>';
    src: url('./fonts/BebasNeue-Regular.ttf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

/* Custom utility class for Revain font */
.font-Revain {
    font-family: 'Revain', cursive, sans-serif;
}

.font-Babas {
    font-family: 'Bebas', cursive, sans-serif;
}

/* Dark theme styles - make all text white */
.dark-theme * {
    color: #ffffff !important;
}

.dark-theme h1,
.dark-theme h2,
.dark-theme h3,
.dark-theme h4,
.dark-theme h5,
.dark-theme h6,
.dark-theme p,
.dark-theme span,
.dark-theme div,
.dark-theme a,
.dark-theme li {
    color: #ffffff !important;
}

/* Light theme styles - make all text black */
body:not(.dark-theme) * {
    color: #000000;
}

body:not(.dark-theme) h1,
body:not(.dark-theme) h2,
body:not(.dark-theme) h3,
body:not(.dark-theme) h4,
body:not(.dark-theme) h5,
body:not(.dark-theme) h6,
body:not(.dark-theme) p,
body:not(.dark-theme) span,
body:not(.dark-theme) div,
body:not(.dark-theme) a,
body:not(.dark-theme) li {
    color: #000000;
}