// imageApi.js - API for managing images from public/images folder

/**
 * Image API for Timber & Tone furniture images
 * Provides easy access to all images in the public/images folder
 */

// Base path for images
const IMAGE_BASE_PATH = '/images/';

// Image data with metadata
const IMAGES_DATA = [
  {
    id: 1,
    filename: '8af6f546ccfee08f88a3673540c56fbc.jpg',
    name: 'Furniture Image 1',
    alt: 'Premium furniture piece',
    category: 'furniture',
    path: `${IMAGE_BASE_PATH}8af6f546ccfee08f88a3673540c56fbc.jpg`
  },
  {
    id: 2,
    filename: 'a0362413090faf45d39a47a08f13f419.jpg',
    name: 'Furniture Image 2',
    alt: 'Modern furniture design',
    category: 'furniture',
    path: `${IMAGE_BASE_PATH}a0362413090faf45d39a47a08f13f419.jpg`
  },
  {
    id: 3,
    filename: 'a34a0281ae00905a33025d59efd4fce3.jpg',
    name: 'Furniture Image 3',
    alt: 'Classic furniture style',
    category: 'furniture',
    path: `${IMAGE_BASE_PATH}a34a0281ae00905a33025d59efd4fce3.jpg`
  },
  {
    id: 4,
    filename: 'cffd9268db2132ef109d51f55bdc1b91.jpg',
    name: 'Furniture Image 4',
    alt: 'Elegant furniture piece',
    category: 'furniture',
    path: `${IMAGE_BASE_PATH}cffd9268db2132ef109d51f55bdc1b91.jpg`
  }
];

/**
 * Get all images
 * @returns {Array} Array of all image objects
 */
export const getAllImages = () => {
  return IMAGES_DATA;
};


/**
 * Get image by ID
 * @param {number} id - Image ID
 * @returns {Object|null} Image object or null if not found
 */
export const getImageById = (id) => {
  return IMAGES_DATA.find(image => image.id === id) || null;
};

/**
 * Get image by filename
 * @param {string} filename - Image filename
 * @returns {Object|null} Image object or null if not found
 */
export const getImageByFilename = (filename) => {
  return IMAGES_DATA.find(image => image.filename === filename) || null;
};

/**
 * Get all image paths only
 * @returns {Array} Array of image paths
 */
export const getAllImagePaths = () => {
  return IMAGES_DATA.map(image => image.path);
};

/**
 * Get random image
 * @returns {Object} Random image object
 */
export const getRandomImage = () => {
  const randomIndex = Math.floor(Math.random() * IMAGES_DATA.length);
  return IMAGES_DATA[randomIndex];
};

/**
 * Get images by category
 * @param {string} category - Image category
 * @returns {Array} Array of images in the specified category
 */
export const getImagesByCategory = (category) => {
  return IMAGES_DATA.filter(image => image.category === category);
};

/**
 * Get first N images
 * @param {number} count - Number of images to return
 * @returns {Array} Array of first N images
 */
export const getFirstImages = (count) => {
  return IMAGES_DATA.slice(0, count);
};

/**
 * Get image URLs for carousel/gallery
 * @returns {Array} Array of objects with src and alt for image display
 */
export const getImageGallery = () => {
  return IMAGES_DATA.map(image => ({
    src: image.path,
    alt: image.alt,
    name: image.name,
    id: image.id
  }));
};

/**
 * Check if image exists by ID
 * @param {number} id - Image ID
 * @returns {boolean} True if image exists
 */
export const imageExists = (id) => {
  return IMAGES_DATA.some(image => image.id === id);
};

/**
 * Get total number of images
 * @returns {number} Total count of images
 */
export const getImageCount = () => {
  return IMAGES_DATA.length;
};

// Export default object with all functions
export default {
  getAllImages,
  getImageById,
  getImageByFilename,
  getAllImagePaths,
  getRandomImage,
  getImagesByCategory,
  getFirstImages,
  getImageGallery,
  imageExists,
  getImageCount,
  IMAGES_DATA
};
