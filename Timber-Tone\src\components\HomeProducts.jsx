import React, { useState, useEffect } from 'react'
import { getAllImages } from '../api/imageApi'

function HomeProducts() {
  const [images, setImages] = useState([])

  useEffect(() => {
    // Fetch images from the API
    const fetchedImages = getAllImages()
    setImages(fetchedImages)
  }, [])

  return (
    <div className='wrapper px-4 flex flex-col gap-[10rem] pb-[10rem]'>
      <div className='flex justify-between align-center'>
        <div className='w-[38vw] h-[60vh] bg-black overflow-hidden rounded-lg'>
          <img
            src={images[0]?.path || ""}
            alt={images[0]?.alt || "Furniture image"}
            className='w-full h-full object-cover'
          />
        </div>
        <div className='w-[48vw] h-[90vh] bg-black overflow-hidden rounded-lg'>
          <img
            src={images[1]?.path || ""}
            alt={images[1]?.alt || "Furniture image"}
            className='w-full h-full object-cover'
          />
        </div>
      </div>

      <div className='flex justify-between align-center'>
        <div className='w-[60vw] h-[75vh] bg-black overflow-hidden rounded-lg'>
          <img
            src={images[2]?.path || ""}
            alt={images[2]?.alt || "Furniture image"}
            className='w-full h-full object-cover'
          />
        </div>
        <div className='w-[18vw] h-[46vh] bg-black overflow-hidden rounded-lg'>
          <img
            src={images[3]?.path || ""}
            alt={images[3]?.alt || "Furniture image"}
            className='w-full h-full object-cover'
          />
        </div>
      </div>

    </div>
  )
}

export default HomeProducts
