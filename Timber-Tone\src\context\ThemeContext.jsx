// ThemeContext.jsx
import { createContext, useContext, useEffect, useState } from "react";

export const ThemeContext = createContext();

// Custom hook to use theme context
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState("light");  //BY default theme is light
// console.log(ThemeProvider)

  // Initialize smooth scrolling
  useEffect(() => {
    // Add smooth scrolling to the document
    document.documentElement.style.scrollBehavior = 'smooth';

    return () => {
      document.documentElement.style.scrollBehavior = 'auto';
    };
  }, []);

  // Load theme from localStorage
  useEffect(() => {
    // there is 'light' theme in storedTheme
    const storedTheme = localStorage.getItem("theme") || "light";
    // console.log(setTheme)
    setTheme(storedTheme);
    document.documentElement.className = storedTheme;

    // Apply background color and text color based on theme
    if (storedTheme === "dark") {
        //For dark theme
      document.body.style.backgroundColor = "#1a1a1a";
      document.body.style.color = "#ffffff";
      // Make all text white in dark mode
      document.documentElement.style.setProperty('--text-color', '#ffffff');
      document.body.classList.add('dark-theme');
    } else {
        //For light theme
      document.body.style.backgroundColor = "#ECEAE5";
      document.body.style.color = "#000000";
      // Make all text black in light mode
      document.documentElement.style.setProperty('--text-color', '#000000');
      document.body.classList.remove('dark-theme');
    }
  }, []);

  // Toggle function
  const toggleTheme = () => {
    const newTheme = theme === "light" ? "dark" : "light";
    setTheme(newTheme);
    document.documentElement.className = newTheme;
    localStorage.setItem("theme", newTheme);

    // Apply background color and text color based on new theme
    if (newTheme === "dark") {
      document.body.style.backgroundColor = "#1a1a1a";
      document.body.style.color = "#ffffff";
      // Make all text white in dark mode
      document.documentElement.style.setProperty('--text-color', '#ffffff');
      document.body.classList.add('dark-theme');
    } else {
      document.body.style.backgroundColor = "#ECEAE5";
      document.body.style.color = "#000000";
      // Make all text black in light mode
      document.documentElement.style.setProperty('--text-color', '#000000');
      document.body.classList.remove('dark-theme');
    }
  };

  const isDarkMode = theme === "dark";

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme, isDarkMode }}>
      {children}
    </ThemeContext.Provider>
  );
};
