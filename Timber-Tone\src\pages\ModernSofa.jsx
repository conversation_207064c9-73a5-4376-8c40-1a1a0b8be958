import React from 'react'

function ModernSofa() {
  return (
    <div className="min-h-screen bg-gray-50 py-20 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Modern Sofas</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Contemporary designs that bring elegance and comfort to your living space
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <img 
              src="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop" 
              alt="Modern Sofa 1" 
              className="w-full h-64 object-cover"
            />
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-2">Minimalist Sectional</h3>
              <p className="text-gray-600 mb-4">Clean lines and premium materials</p>
              <div className="flex justify-between items-center">
                <span className="text-2xl font-bold text-amber-600">$1,899</span>
                <button className="bg-amber-600 text-white px-4 py-2 rounded-lg hover:bg-amber-700">
                  Add to Cart
                </button>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <img 
              src="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop" 
              alt="Modern Sofa 2" 
              className="w-full h-64 object-cover"
            />
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-2">Contemporary Loveseat</h3>
              <p className="text-gray-600 mb-4">Perfect for modern apartments</p>
              <div className="flex justify-between items-center">
                <span className="text-2xl font-bold text-amber-600">$1,299</span>
                <button className="bg-amber-600 text-white px-4 py-2 rounded-lg hover:bg-amber-700">
                  Add to Cart
                </button>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <img 
              src="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop" 
              alt="Modern Sofa 3" 
              className="w-full h-64 object-cover"
            />
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-2">Designer Accent Sofa</h3>
              <p className="text-gray-600 mb-4">Statement piece for your home</p>
              <div className="flex justify-between items-center">
                <span className="text-2xl font-bold text-amber-600">$2,199</span>
                <button className="bg-amber-600 text-white px-4 py-2 rounded-lg hover:bg-amber-700">
                  Add to Cart
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ModernSofa
