{"name": "gsap", "version": "3.13.0", "description": "GSAP is a framework-agnostic JavaScript animation library that turns developers into animation superheroes. Build high-performance animations that work in **every** major browser. Animate CSS, SVG, canvas, React, Vue, WebGL, colors, strings, motion paths, generic objects...anything JavaScript can touch! The ScrollTrigger plugin lets you create jaw-dropping scroll-based animations with minimal code. No other library delivers such advanced sequencing, reliability, and tight control while solving real-world problems on millions of sites. GSAP works around countless browser inconsistencies; your animations **just work**. At its core, GSAP is a high-speed property manipulator, updating values over time with extreme accuracy.", "homepage": "https://gsap.com", "module": "index.js", "main": "dist/gsap.js", "types": "types/index.d.ts", "sideEffects": false, "keywords": ["GSAP", "GreenSock", "animation", "MotionPathPlugin", "motion", "motionPath", "matchMedia", "easing", "ScrollTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Observer", "JavaScript", "PixiPlugin", "CustomEase", "Flip", "SVG", "3D", "2D", "transform", "morph", "morphing", "tweening", "Webflow"], "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "web": "https://gsap.com"}], "license": "Standard 'no charge' license: https://gsap.com/standard-license.", "bugs": {"url": "https://gsap.com/community/"}, "repository": {"type": "git", "url": "https://github.com/greensock/GSAP"}}