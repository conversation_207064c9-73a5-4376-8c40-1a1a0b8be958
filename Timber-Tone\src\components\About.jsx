import React from 'react'

function About() {
  return (
    <div className='wrapper p-4 min-h-screen border-t-[1px] border-black font-Haas'>
        <h5 className='text-[1.1vw]'>(All_Products)</h5>
      <div className='pt-4'>
        <a className='text-amber-900 text-[6vw]' href="./products">See All Products</a>
      </div>
      <div className='pt-24 items-center justify-center flex'>
        <p className='text-[3vw]'><span className='text-[1.1vw] mr-[5rem]'>(About TimerTone)</span>Using sofa is more than just furniture—it’s where life happens. At Timbertone, we design sofas that support your lifestyle with unmatched comfort, style, and durability. From movie nights to lazy Sundays, our pieces are made to be lived in and loved for years to come. With a wide variety of colors, sizes, and styles, there's a Timbertone sofa for every home.</p>
      </div>
      <div>
        <section className='flex flex-col gap-8 pt-14'>
            <p>
                Founded by visionary designer <PERSON>, <br />
                Timbertone Sofa debuted its first collection in 2023. <br />
                <br />
                With a commitment to craftsmanship and collaboration, <br />
                the brand frequently partners with leading architects <br />
                and designers worldwide, guided by Personick's <br />
                refined, brutalist-inspired aesthetic.
            </p>

            <p>
                Each piece from Timbertone Sofa is a harmonious <br />
                blend of function and artistry—designed not only <br />
                to elevate everyday living but also to serve as <br />
                timeless, collectible works of design.
            </p>
        </section>
      </div>
    </div>
  )
}

export default About
