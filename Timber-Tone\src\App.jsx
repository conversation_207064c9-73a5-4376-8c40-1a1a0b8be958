import React from 'react'
import { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom'
import { ThemeProvider } from './context/ThemeContext'
import Navbar from './components/Navbar'
import Home from './pages/Main'
import Products from './pages/Products'
import HomeProducts from './components/HomeProducts'

function App() {
  return (
    <ThemeProvider>
      <Router>
        <div className="min-h-screen bg-[#ECEAE5]">
          <Navbar />
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/products" element={<Products />} />
          </Routes>
        </div>
      </Router>
    </ThemeProvider>
  )
}

export default App
