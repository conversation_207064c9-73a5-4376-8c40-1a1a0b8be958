import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import Navbar from './components/Navbar'
import Home from './pages/Home'
import SofaBeds from './pages/SofaBeds'
import ModernSofa from './pages/ModernSofa'
import ClassicSofa from './pages/ClassicSofa'
import SectionalSofa from './pages/SectionalSofa'
import SideTable from './pages/SideTable'

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/sofa-beds" element={<SofaBeds />} />
          <Route path="/modern-sofa" element={<ModernSofa />} />
          <Route path="/classic-sofa" element={<ClassicSofa />} />
          <Route path="/sectional-sofa" element={<SectionalSofa />} />
          <Route path="/side-table" element={<SideTable />} />
        </Routes>
      </div>
    </Router>
  )
}

export default App
