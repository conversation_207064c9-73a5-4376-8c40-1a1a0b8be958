import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import Navbar from './components/Navbar'
import Home from './pages/Home'
import Products from './pages/Products'

// Simple placeholder components for missing pages
const SofaBeds = () => (
  <div className="min-h-screen bg-gray-50 py-20 px-4">
    <div className="max-w-7xl mx-auto text-center">
      <h1 className="text-4xl font-bold text-gray-900 mb-4">Sofa Beds</h1>
      <p className="text-xl text-gray-600">Coming Soon - Our collection of comfortable sofa beds</p>
    </div>
  </div>
)

const ModernSofa = () => (
  <div className="min-h-screen bg-gray-50 py-20 px-4">
    <div className="max-w-7xl mx-auto text-center">
      <h1 className="text-4xl font-bold text-gray-900 mb-4">Modern Sofas</h1>
      <p className="text-xl text-gray-600">Coming Soon - Our collection of modern sofas</p>
    </div>
  </div>
)

const ClassicSofa = () => (
  <div className="min-h-screen bg-gray-50 py-20 px-4">
    <div className="max-w-7xl mx-auto text-center">
      <h1 className="text-4xl font-bold text-gray-900 mb-4">Classic Sofas</h1>
      <p className="text-xl text-gray-600">Coming Soon - Our collection of classic sofas</p>
    </div>
  </div>
)

const SectionalSofa = () => (
  <div className="min-h-screen bg-gray-50 py-20 px-4">
    <div className="max-w-7xl mx-auto text-center">
      <h1 className="text-4xl font-bold text-gray-900 mb-4">Sectional Sofas</h1>
      <p className="text-xl text-gray-600">Coming Soon - Our collection of sectional sofas</p>
    </div>
  </div>
)

const SideTable = () => (
  <div className="min-h-screen bg-gray-50 py-20 px-4">
    <div className="max-w-7xl mx-auto text-center">
      <h1 className="text-4xl font-bold text-gray-900 mb-4">Side Tables</h1>
      <p className="text-xl text-gray-600">Coming Soon - Our collection of side tables</p>
    </div>
  </div>
)

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/sofa-beds" element={<SofaBeds />} />
          <Route path="/modern-sofa" element={<ModernSofa />} />
          <Route path="/classic-sofa" element={<ClassicSofa />} />
          <Route path="/sectional-sofa" element={<SectionalSofa />} />
          <Route path="/side-table" element={<SideTable />} />
          <Route path="/products" element={<Products />} />
        </Routes>
      </div>
    </Router>
  )
}

export default App
