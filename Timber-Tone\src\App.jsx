import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import Navbar from './components/Navbar'
import Home from './pages/Home'
import App from './pages/App'
import Products from './pages/Products'
import Information from './pages/Information'

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/Home" element={<Home />} />
          <Route path="/Index" element={<Products />} />
          <Route path="/Information" element={<Information />} />
        </Routes>
      </div>
    </Router>
  )
}

export default App
