import React, { Suspense } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import Navbar from './components/Navbar'
import Home from './pages/Home'
import SofaBeds from './pages/SofaBeds'
import ModernSofa from './pages/ModernSofa'
import ClassicSofa from './pages/ClassicSofa'
import SectionalSofa from './pages/SectionalSofa'
import SideTable from './pages/SideTable'
import NotFound from './pages/NotFound'

// Loading component for better UX
const LoadingSpinner = () => (
  <div className="min-h-screen bg-gray-50 flex items-center justify-center">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto mb-4"></div>
      <p className="text-gray-600">Loading...</p>
    </div>
  </div>
)

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <Suspense fallback={<LoadingSpinner />}>
          <Routes>
            {/* Home Route */}
            <Route path="/" element={<Home />} />

            {/* Product Category Routes */}
            <Route path="/sofa-beds" element={<SofaBeds />} />
            <Route path="/modern-sofa" element={<ModernSofa />} />
            <Route path="/classic-sofa" element={<ClassicSofa />} />
            <Route path="/sectional-sofa" element={<SectionalSofa />} />
            <Route path="/side-table" element={<SideTable />} />

            {/* Alternative route names for better SEO */}
            <Route path="/sofabeds" element={<Navigate to="/sofa-beds" replace />} />
            <Route path="/modern" element={<Navigate to="/modern-sofa" replace />} />
            <Route path="/classic" element={<Navigate to="/classic-sofa" replace />} />
            <Route path="/sectional" element={<Navigate to="/sectional-sofa" replace />} />
            <Route path="/tables" element={<Navigate to="/side-table" replace />} />
            <Route path="/side-tables" element={<Navigate to="/side-table" replace />} />

            {/* Legacy routes redirect */}
            <Route path="/home" element={<Navigate to="/" replace />} />
            <Route path="/index" element={<Navigate to="/" replace />} />

            {/* 404 Catch-all Route */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </Suspense>
      </div>
    </Router>
  )
}

export default App
