import React from 'react'
import { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom'
import { ThemeProvider } from './context/ThemeContext'
import Navbar from './components/Navbar'
import Home from './pages/Home'
import Products from './pages/Products'
import HomeProducts from './components/HomeProducts'

function App() {
  return (
    <ThemeProvider>
      <Router>
        <div className="min-h-screen bg-[#ECEAE5]">
          <Navbar />
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/products" element={<Products />} />
          </Routes>
        </div>
      </Router>
      <HomeProducts />
    </ThemeProvider>
  )
}

export default App
