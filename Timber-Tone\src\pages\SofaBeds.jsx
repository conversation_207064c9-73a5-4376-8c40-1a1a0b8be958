import React from 'react'

function SofaBeds() {
  return (
    <div className="min-h-screen bg-gray-50 py-20 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Sofa Beds</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover our collection of comfortable and stylish sofa beds that transform your space
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Sample products */}
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <img 
              src="https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=400&h=300&fit=crop" 
              alt="Sofa Bed 1" 
              className="w-full h-64 object-cover"
            />
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-2">Comfort Plus Sofa Bed</h3>
              <p className="text-gray-600 mb-4">Perfect for guests and small spaces</p>
              <div className="flex justify-between items-center">
                <span className="text-2xl font-bold text-amber-600">$899</span>
                <button className="bg-amber-600 text-white px-4 py-2 rounded-lg hover:bg-amber-700">
                  Add to Cart
                </button>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <img 
              src="https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=400&h=300&fit=crop" 
              alt="Sofa Bed 2" 
              className="w-full h-64 object-cover"
            />
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-2">Luxury Convertible Sofa</h3>
              <p className="text-gray-600 mb-4">Premium comfort and style</p>
              <div className="flex justify-between items-center">
                <span className="text-2xl font-bold text-amber-600">$1,299</span>
                <button className="bg-amber-600 text-white px-4 py-2 rounded-lg hover:bg-amber-700">
                  Add to Cart
                </button>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <img 
              src="https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=400&h=300&fit=crop" 
              alt="Sofa Bed 3" 
              className="w-full h-64 object-cover"
            />
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-2">Modern Sleeper Sofa</h3>
              <p className="text-gray-600 mb-4">Contemporary design meets functionality</p>
              <div className="flex justify-between items-center">
                <span className="text-2xl font-bold text-amber-600">$1,099</span>
                <button className="bg-amber-600 text-white px-4 py-2 rounded-lg hover:bg-amber-700">
                  Add to Cart
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SofaBeds
