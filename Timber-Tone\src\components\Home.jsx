import React from 'react'
import { useTheme } from '../context/ThemeContext'

function Home() {
  const { isDarkMode } = useTheme()

  return (
    <div>
       <div className='text-center'>
      <h1 className={`text-[40vw] pt-19 leading-none letter-spacing-[-0.02em] font-bold mb-8 font-MegaBuns tracking-wide ${
        isDarkMode ? 'text-white' : 'text-[#28282A]'
      }`}>
        Tone
      </h1>
      </div>
    </div>
  )
}

export default Home
