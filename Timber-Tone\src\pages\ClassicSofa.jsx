import React from 'react'

function ClassicSofa() {
  return (
    <div className="min-h-screen bg-gray-50 py-20 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Classic Sofas</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Timeless designs that never go out of style, crafted with traditional elegance
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <img 
              src="https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=400&h=300&fit=crop" 
              alt="Classic Sofa 1" 
              className="w-full h-64 object-cover"
            />
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-2">Victorian Chesterfield</h3>
              <p className="text-gray-600 mb-4">Traditional button-tufted design</p>
              <div className="flex justify-between items-center">
                <span className="text-2xl font-bold text-amber-600">$2,499</span>
                <button className="bg-amber-600 text-white px-4 py-2 rounded-lg hover:bg-amber-700">
                  Add to Cart
                </button>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <img 
              src="https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=400&h=300&fit=crop" 
              alt="Classic Sofa 2" 
              className="w-full h-64 object-cover"
            />
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-2">English Roll Arm Sofa</h3>
              <p className="text-gray-600 mb-4">Elegant and comfortable classic</p>
              <div className="flex justify-between items-center">
                <span className="text-2xl font-bold text-amber-600">$1,899</span>
                <button className="bg-amber-600 text-white px-4 py-2 rounded-lg hover:bg-amber-700">
                  Add to Cart
                </button>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <img 
              src="https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=400&h=300&fit=crop" 
              alt="Classic Sofa 3" 
              className="w-full h-64 object-cover"
            />
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-2">Traditional Wingback</h3>
              <p className="text-gray-600 mb-4">Sophisticated and timeless</p>
              <div className="flex justify-between items-center">
                <span className="text-2xl font-bold text-amber-600">$2,199</span>
                <button className="bg-amber-600 text-white px-4 py-2 rounded-lg hover:bg-amber-700">
                  Add to Cart
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ClassicSofa
