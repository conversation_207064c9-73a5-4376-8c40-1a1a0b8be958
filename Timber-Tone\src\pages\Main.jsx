import React from 'react'
import { useTheme } from '../context/ThemeContext'
import Home from '../components/Home'
import HomeProducts from '../components/HomeProducts'
import About from '../components/About'
import Vdo from '../components/vdo'

function Main() {
  const { isDarkMode } = useTheme()

  return (
    <div className={`w-full h-[90vh] ${isDarkMode ? 'bg-[#1a1a1a]' : 'bg-[#ECEAE5]'}`}>
      <Home />
      <HomeProducts />
      <About />
      <Vdo />
    </div>
  )
}

export default Main
