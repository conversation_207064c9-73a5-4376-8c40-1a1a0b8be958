import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { useTheme } from '../context/ThemeContext'

function Navbar() {
    const { isDarkMode, toggleTheme } = useTheme()

    const NavItems = [
        {label: 'Home', href: '/'},
        {label: 'Products', href: '/products'},
    ]

  return (
    <div className='flex justify-between items-center p-4'>
        <Link to="/" className='text-2xl font-bold'>
            Timber & Tone
        </Link>
      <ul className='flex gap-8 items-center'>
        {NavItems.map(item => (
            <li key={item.label}>
                <Link
                    to={item.href}
                    className='text-gray-700 hover:text-amber-600 transition-colors duration-200'
                >
                    {item.label}
                </Link>
            </li>
        ))}
        <li
            className='cursor-pointer p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200'
            onClick={toggleTheme}
            title={isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
        >
            <div className='w-[3vw] h-[1.5vw] bg-gradient-to-r from-yellow-400 to-gray-800 rounded-full relative overflow-hidden transition-all'>
                <div
                    className={`absolute w-[1.2vw] h-[1.2vw] bg-white rounded-full top-[0.15vw] transition-transform duration-300 shadow-md ${
                        isDarkMode ? 'translate-x-[1.65vw]' : 'translate-x-[0.15vw]'
                    }`}
                >
                    <span className='absolute inset-0 flex items-center justify-center text-[0.6vw]'>
                        {isDarkMode ? '🌙' : '☀️'}
                    </span>
                </div>
            </div>
        </li>
      </ul>
    </div>
  )
}

export default Navbar
