import React from 'react'
import { <PERSON> } from 'react-router-dom'

function Navbar() {
    const NavItems = [
        {label: 'Home', href: '/'},
        {label: 'Products', href: '/products'},
    ]
  return (
    <div className='flex justify-between items-center p-4'>
        <Link to="/" className='text-2xl font-bold'>
            Timber & Tone
        </Link>
      <ul className='flex gap-8'>
        {NavItems.map(item => (
            <li key={item.label}>
                <Link
                    to={item.href}
                    className='text-gray-700 '
                >
                    {item.label}
                </Link>
            </li>
        ))}
      </ul>
    </div>
  )
}

export default Navbar
