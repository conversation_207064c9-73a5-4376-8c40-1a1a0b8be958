import React, { useState, useEffect } from 'react'
import { Link, useLocation, useNavigate } from 'react-router-dom'

function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const location = useLocation()
  const navigate = useNavigate()

  const navItems = [
    { href: "/sofa-beds", label: "Sofa Beds", id: "sofa-beds" },
    { href: "/modern-sofa", label: "Modern Sofa", id: "modern-sofa" },
    { href: "/classic-sofa", label: "Classic Sofa", id: "classic-sofa" },
    { href: "/sectional-sofa", label: "Sectional Sofa", id: "sectional-sofa" },
    { href: "/side-table", label: "Side Table", id: "side-table" }
  ]

  const isActive = (path) => location.pathname === path

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMenuOpen(false)
  }, [location])

  // Handle navigation with smooth scrolling to top
  const handleNavigation = (href) => {
    navigate(href)
    window.scrollTo({ top: 0, behavior: 'smooth' })
    setIsMenuOpen(false)
  }

  return (
    <nav className='bg-white shadow-md sticky top-0 z-50'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
        <div className='flex justify-between items-center h-16'>
          {/* Logo */}
          <div className='flex-shrink-0'>
            <Link
              to="/"
              className='text-2xl font-bold text-amber-800 hover:text-amber-600 transition-colors duration-200'
              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
            >
              Timber & Tone
            </Link>
          </div>

          {/* Desktop Navigation */}
          <ul className='hidden md:flex space-x-8'>
            {navItems.map((item, index) => (
              <li key={index}>
                <Link
                  to={item.href}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 hover:bg-amber-50 ${
                    isActive(item.href)
                      ? 'text-amber-600 bg-amber-50'
                      : 'text-gray-700 hover:text-amber-600'
                  }`}
                  onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                  aria-label={`Navigate to ${item.label} page`}
                >
                  {item.label}
                </Link>
              </li>
            ))}
          </ul>

          {/* Mobile menu button */}
          <div className='md:hidden'>
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className='text-gray-700 hover:text-amber-600 focus:outline-none focus:text-amber-600 p-2'
              aria-label='Toggle navigation menu'
              aria-expanded={isMenuOpen}
            >
              <svg className='h-6 w-6' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
                {isMenuOpen ? (
                  <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M6 18L18 6M6 6l12 12' />
                ) : (
                  <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M4 6h16M4 12h16M4 18h16' />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className='md:hidden'>
            <ul className='px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200'>
              {navItems.map((item, index) => (
                <li key={index}>
                  <Link
                    to={item.href}
                    className={`block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 hover:bg-amber-50 ${
                      isActive(item.href)
                        ? 'text-amber-600 bg-amber-50'
                        : 'text-gray-700 hover:text-amber-600'
                    }`}
                    onClick={() => handleNavigation(item.href)}
                    aria-label={`Navigate to ${item.label} page`}
                  >
                    {item.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </nav>
  )
}

export default Navbar
