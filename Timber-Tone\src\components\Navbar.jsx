import React from 'react'

function Navbar() {
    const NavItems = [
        {label: 'Sofa Beds', href: '#sofa-beds'},
        {label: 'Modern Sofa', href: '#modern-sofa'},
        {label: 'Classic Sofa', href: '#classic-sofa'},
        {label: 'Sectional Sofa', href: '#sectional-sofa'},
        {label: 'Side Table', href: '#side-table'}
    ]
  return (
    <div className='flex justify-between p-4'>
        <h2>Timber Tone</h2>
      <ul className='flex justify-beteween gap-10'>
        {NavItems.map(item => (
            <li><a href={item.href}>{item.label}</a></li>
        ))}
      </ul>
    </div>
  )
}

export default Navbar
