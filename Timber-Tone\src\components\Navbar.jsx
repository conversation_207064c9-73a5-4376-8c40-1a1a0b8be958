import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'

function Navbar() {
    const [isDarkMode, setIsDarkMode] = useState(false)

    const NavItems = [
        {label: 'Home', href: '/'},
        {label: 'Products', href: '/products'},
    ]

    // Load theme from localStorage on component mount
    useEffect(() => {
        const savedTheme = localStorage.getItem('theme')
        if (savedTheme === 'dark') {
            setIsDarkMode(true)
            document.documentElement.classList.add('dark')
        }
    }, [])

    // Toggle theme function
    const toggleTheme = () => {
        setIsDarkMode(!isDarkMode)
        if (!isDarkMode) {
            document.documentElement.classList.add('dark')
            localStorage.setItem('theme', 'dark')
        } else {
            document.documentElement.classList.remove('dark')
            localStorage.setItem('theme', 'light')
        }
    }

  return (
    <div className='flex justify-between items-center p-4'>
        <Link to="/" className='text-2xl font-bold'>
            Timber & Tone
        </Link>
      <ul className='flex gap-8 items-center'>
        {NavItems.map(item => (
            <li key={item.label}>
                <Link
                    to={item.href}
                    className='text-gray-700 hover:text-amber-600 transition-colors duration-200'
                >
                    {item.label}
                </Link>
            </li>
        ))}
        <li
            className='toggle-theme w-[3vw] h-[1.5vw] bg-gradient-to-r from-yellow-400 to-gray-800 rounded-full cursor-pointer relative overflow-hidden transition-all duration-300 hover:scale-110'
            onClick={toggleTheme}
            title={isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
        >
            <div
                className={`absolute w-[1.2vw] h-[1.2vw] bg-white rounded-full top-[0.15vw] transition-transform duration-300 shadow-md ${
                    isDarkMode ? 'translate-x-[1.65vw]' : 'translate-x-[0.15vw]'
                }`}
            >
                <span className='absolute inset-0 flex items-center justify-center text-[0.6vw]'>
                    {isDarkMode ? '🌙' : '☀️'}
                </span>
            </div>
        </li>
      </ul>
    </div>
  )
}

export default Navbar
