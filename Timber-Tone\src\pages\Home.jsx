import React from 'react'

function Home() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-amber-50 to-orange-50 py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <h1 className="text-6xl md:text-8xl font-bold mb-8 tracking-wide text-gray-900" style={{fontFamily: 'MegaBuns'}}>
              Timber & Tone
            </h1>
            <h2 className="text-3xl md:text-4xl font-bold text-amber-600 mb-8" style={{fontFamily: 'MegaBuns'}}>
              Premium Furniture Collection
            </h2>
            <p className="text-xl text-gray-600 leading-relaxed max-w-2xl mx-auto mb-8">
              Discover premium furniture that combines timeless craftsmanship with modern design.
              Create the perfect ambiance for your home with our curated collection.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-amber-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-amber-700 transition-colors duration-200 shadow-lg">
                Shop Collection
              </button>
              <button className="border-2 border-amber-600 text-amber-600 px-8 py-4 rounded-lg font-semibold hover:bg-amber-600 hover:text-white transition-all duration-200">
                View Catalog
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4" style={{fontFamily: 'MegaBuns'}}>
              Why Choose Us?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              We're committed to delivering exceptional furniture that transforms your living space
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-amber-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl">★</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2" style={{fontFamily: 'MegaBuns'}}>Premium Quality</h3>
              <p className="text-gray-600">Handcrafted with the finest materials and attention to detail</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-amber-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl">🚚</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2" style={{fontFamily: 'MegaBuns'}}>Fast Delivery</h3>
              <p className="text-gray-600">Quick and secure delivery to your doorstep within 5-7 business days</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-amber-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl">💝</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2" style={{fontFamily: 'MegaBuns'}}>Customer Love</h3>
              <p className="text-gray-600">Over 10,000 satisfied customers with 5-star reviews</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Home
